import { inject, Injectable } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import {
    combineLatest,
    filter,
    map,
    startWith
} from 'rxjs';
import { LeadInitError } from '../../lead/lead.service';
import { UserAuthorizationService } from '../../user-authorization/user-authorization.service';
import { LoanStateService } from './loan-state.service';

@Injectable()
export class LoanEditingState {
  private loanStateService = inject(LoanStateService);
  private userAuthorizationService = inject(UserAuthorizationService);

  /*
  * All reason a loan may not be editable
  */
  private isLoanEditingDisabled$ = combineLatest([
    this.loanStateService.isLoanDeactivated$.pipe(startWith(false)),
    this.loanStateService.isLoanXpCompleted$.pipe(startWith(false)),
    this.loanStateService.loanLoadingError$.pipe(
      filter((error): error is LeadInitError => !!error),
      startWith(null),
    ),
    this.userAuthorizationService.isLoanArchived$,
  ]).pipe(
    map(
      ([isLoanDeactivated, isLoanXpCompleted, isLoanLoadingError, isLoanArchived]) =>
        isLoanDeactivated || isLoanXpCompleted || isLoanLoadingError === LeadInitError.Unsupported || isLoanArchived,
    ),
    takeUntilDestroyed(),
  );


  // Public signal
  public isLoanEditingDisabled = toSignal(this.isLoanEditingDisabled$);

  
}
