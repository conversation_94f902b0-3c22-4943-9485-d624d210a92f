import { InjectionToken, Provider } from '@angular/core';
import { AutoSaveTriggerService } from '../../save-trigger/auto-save.service';
import { SaveTrigger } from '../../save-trigger/save-trigger';
import { STATE_SERVICES } from '../abstract-entity-state.service';
import { UPDATE_HANDLERS } from '../abstract-update-handler.service';
import { LoanAutoSaveTriggerService } from './loan-auto-save.service';
import { LoanFormRef, LoanFormService } from './loan-form.service';
import { LoanInfoFormListenerService } from './loan-info-form-listener.service';
import { LoanPurposeHandlerService } from './loan-purpose-handler.service';
import { LoanStateService } from './loan-state.service';
import { LoanUpdateHandlerService } from './loan-update-handler.service';
import { LoanValidationHandlerService } from './loan-validation-handler.service';

export const LOAN_SAVE_TRIGGER = new InjectionToken<SaveTrigger>('LOAN_SAVE_TRIGGER');
export const LOAN_AUTO_SAVE_TRIGGER = new InjectionToken<AutoSaveTriggerService>(
  'LOAN_AUTO_SAVE_TRIGGER',
);

export function provideLoanState(): Provider[] {
  return [
    LoanStateService,
    LoanEditingState,
    LoanFormService,
    LoanFormRef,
    LoanInfoFormListenerService,
    LoanUpdateHandlerService,
    LoanPurposeHandlerService,
    LoanValidationHandlerService,
    { provide: LOAN_AUTO_SAVE_TRIGGER, useClass: LoanAutoSaveTriggerService },
    { provide: LOAN_SAVE_TRIGGER, useExisting: LOAN_AUTO_SAVE_TRIGGER },
    { provide: UPDATE_HANDLERS, useExisting: LoanUpdateHandlerService, multi: true },
    { provide: STATE_SERVICES, useExisting: LoanStateService, multi: true },
  ];
}
